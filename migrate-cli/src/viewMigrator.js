const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const CodeMigrator = require('./codeMigrator');

/**
 * 视图迁移器
 * 专门处理 views 目录的迁移和转换
 */
class ViewMigrator extends CodeMigrator {
  constructor(inputPath, outputPath, options = {}) {
    super(inputPath, {
      srcDir: 'src/views',
      outputPath: outputPath,
      outputSrcDir: 'src/views',
      includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
      excludePatterns: ['node_modules/**', 'dist/**', 'build/**', '**/*.test.js', '**/*.spec.js'],
      copyMode: true,
      ...options
    });

    this.viewStats = {
      totalViews: 0,
      migratedViews: 0,
      failedViews: 0,
      skippedViews: 0,
      viewTypes: {
        list: 0,         // 列表页面
        detail: 0,       // 详情页面
        form: 0,         // 表单页面
        dashboard: 0,    // 仪表板页面
        auth: 0,         // 认证页面
        error: 0,        // 错误页面
        other: 0         // 其他页面
      },
      routerFiles: [],
      storeFiles: []
    };
  }

  /**
   * 执行视图迁移
   */
  async migrateViews() {
    try {
      console.log(chalk.blue('📄 开始迁移 Vue 视图...'));
      console.log(chalk.gray(`源路径: ${path.join(this.inputPath, this.options.srcDir)}`));
      console.log(chalk.gray(`目标路径: ${path.join(this.outputPath, this.options.outputSrcDir)}`));

      // 检查源视图目录是否存在
      const sourceViewsPath = path.join(this.inputPath, this.options.srcDir);
      if (!await fs.pathExists(sourceViewsPath)) {
        console.log(chalk.yellow('⚠️  源项目中没有找到 views 目录，跳过视图迁移'));
        return { success: true, message: 'No views directory found' };
      }

      // 确保目标目录存在
      await this.ensureOutputDirectory();

      // 执行迁移
      const result = await this.migrate();

      // 分析视图类型
      await this.analyzeViewTypes();

      // 检查路由文件
      await this.checkRouterFiles();

      // 检查状态管理文件
      await this.checkStoreFiles();

      // 打印视图迁移统计
      this.printViewStats();

      return {
        success: true,
        stats: this.stats,
        viewStats: this.viewStats
      };

    } catch (error) {
      console.error(chalk.red('❌ 视图迁移失败:'), error.message);
      throw error;
    }
  }

  /**
   * 分析视图类型
   */
  async analyzeViewTypes() {
    try {
      const files = await this.getFilesToMigrate();
      this.viewStats.totalViews = files.filter(file => file.endsWith('.vue')).length;

      for (const filePath of files) {
        if (path.extname(filePath) === '.vue') {
          const viewType = await this.detectViewType(filePath);
          this.viewStats.viewTypes[viewType]++;
        }
      }
    } catch (error) {
      console.warn(chalk.yellow('⚠️  视图类型分析失败:'), error.message);
    }
  }

  /**
   * 检测视图类型
   */
  async detectViewType(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const fileName = path.basename(filePath, '.vue').toLowerCase();
      const relativePath = path.relative(this.inputPath, filePath).toLowerCase();

      // 基于文件名和路径检测视图类型
      if (fileName.includes('list') || fileName.includes('index') || content.includes('el-table')) {
        return 'list';
      } else if (fileName.includes('detail') || fileName.includes('info') || fileName.includes('view')) {
        return 'detail';
      } else if (fileName.includes('form') || fileName.includes('edit') || fileName.includes('add') || content.includes('el-form')) {
        return 'form';
      } else if (fileName.includes('dashboard') || relativePath.includes('dashboard')) {
        return 'dashboard';
      } else if (fileName.includes('login') || fileName.includes('register') || relativePath.includes('auth')) {
        return 'auth';
      } else if (fileName.includes('error') || fileName.includes('404') || fileName.includes('403')) {
        return 'error';
      } else {
        return 'other';
      }
    } catch (error) {
      return 'other';
    }
  }

  /**
   * 检查路由文件
   */
  async checkRouterFiles() {
    const routerPaths = [
      path.join(this.inputPath, 'src/router/index.js'),
      path.join(this.inputPath, 'src/router/index.ts'),
      path.join(this.inputPath, 'src/router.js'),
      path.join(this.inputPath, 'src/router.ts')
    ];

    for (const routerPath of routerPaths) {
      if (await fs.pathExists(routerPath)) {
        this.viewStats.routerFiles.push(routerPath);
      }
    }
  }

  /**
   * 检查状态管理文件
   */
  async checkStoreFiles() {
    const storePaths = [
      path.join(this.inputPath, 'src/store/index.js'),
      path.join(this.inputPath, 'src/store/index.ts'),
      path.join(this.inputPath, 'src/store.js'),
      path.join(this.inputPath, 'src/store.ts')
    ];

    for (const storePath of storePaths) {
      if (await fs.pathExists(storePath)) {
        this.viewStats.storeFiles.push(storePath);
      }
    }
  }

  /**
   * 迁移 Vue 文件（重写以添加视图特定的转换）
   */
  async migrateVueFile(source, filePath) {
    try {
      // 先执行基础的 Vue 文件迁移
      let transformedCode = await super.migrateVueFile(source, filePath);

      // 应用视图特定的转换
      transformedCode = this.applyViewSpecificTransforms(transformedCode, filePath);

      return transformedCode;
    } catch (error) {
      throw new Error(`视图文件转换失败: ${error.message}`);
    }
  }

  /**
   * 应用视图特定的转换
   */
  applyViewSpecificTransforms(code, filePath) {
    const fileName = path.basename(filePath, '.vue').toLowerCase();

    // 路由相关转换
    code = this.transformRouterUsage(code);

    // 状态管理相关转换
    code = this.transformStoreUsage(code);

    // 页面级组件特定转换
    if (fileName.includes('list') || code.includes('el-table')) {
      code = this.transformListViews(code);
    }

    if (fileName.includes('form') || code.includes('el-form')) {
      code = this.transformFormViews(code);
    }

    if (fileName.includes('dashboard')) {
      code = this.transformDashboardViews(code);
    }

    return code;
  }

  /**
   * 转换路由使用
   */
  transformRouterUsage(code) {
    // Vue Router 4 的变化
    code = code.replace(/this\.\$router\.push\(/g, 'router.push(');
    code = code.replace(/this\.\$route\./g, 'route.');
    
    // 添加必要的导入
    if (code.includes('router.push') || code.includes('route.')) {
      if (!code.includes('import { useRouter') && !code.includes('import { useRoute')) {
        // 在 script setup 或 composition API 中添加导入
        if (code.includes('<script setup>')) {
          code = code.replace(
            '<script setup>',
            '<script setup>\nimport { useRouter, useRoute } from \'vue-router\'\n\nconst router = useRouter()\nconst route = useRoute()'
          );
        }
      }
    }

    return code;
  }

  /**
   * 转换状态管理使用
   */
  transformStoreUsage(code) {
    // Vuex 4 的变化
    code = code.replace(/this\.\$store\./g, 'store.');
    
    // 添加必要的导入
    if (code.includes('store.')) {
      if (!code.includes('import { useStore')) {
        if (code.includes('<script setup>')) {
          code = code.replace(
            '<script setup>',
            '<script setup>\nimport { useStore } from \'vuex\'\n\nconst store = useStore()'
          );
        }
      }
    }

    return code;
  }

  /**
   * 转换列表视图
   */
  transformListViews(code) {
    // 列表页面特定的转换
    // 处理分页组件的变化
    code = code.replace(/el-pagination/g, 'el-pagination');
    
    return code;
  }

  /**
   * 转换表单视图
   */
  transformFormViews(code) {
    // 表单页面特定的转换
    return code;
  }

  /**
   * 转换仪表板视图
   */
  transformDashboardViews(code) {
    // 仪表板页面特定的转换
    return code;
  }

  /**
   * 打印视图迁移统计
   */
  printViewStats() {
    console.log('\n' + chalk.bold('📄 视图迁移统计:'));
    console.log(`总视图数: ${this.viewStats.totalViews}`);
    
    console.log('\n视图类型分布:');
    Object.entries(this.viewStats.viewTypes).forEach(([type, count]) => {
      if (count > 0) {
        const typeNames = {
          list: '列表页面',
          detail: '详情页面',
          form: '表单页面',
          dashboard: '仪表板页面',
          auth: '认证页面',
          error: '错误页面',
          other: '其他页面'
        };
        console.log(`  ${typeNames[type]}: ${count} 个`);
      }
    });

    if (this.viewStats.routerFiles.length > 0) {
      console.log('\n📍 发现路由文件:');
      this.viewStats.routerFiles.forEach(file => {
        console.log(`  ${path.relative(this.inputPath, file)}`);
      });
    }

    if (this.viewStats.storeFiles.length > 0) {
      console.log('\n🗃️  发现状态管理文件:');
      this.viewStats.storeFiles.forEach(file => {
        console.log(`  ${path.relative(this.inputPath, file)}`);
      });
    }

    // 调用父类的统计打印
    this.printMigrationStats();
  }

  /**
   * 生成视图迁移报告
   */
  generateViewReport() {
    return {
      timestamp: new Date().toISOString(),
      inputPath: this.inputPath,
      outputPath: this.outputPath,
      stats: this.stats,
      viewStats: this.viewStats,
      recommendations: this.generateViewRecommendations()
    };
  }

  /**
   * 生成视图迁移建议
   */
  generateViewRecommendations() {
    const recommendations = [];

    if (this.viewStats.routerFiles.length > 0) {
      recommendations.push('检查路由文件是否需要升级到 Vue Router 4 语法');
    }

    if (this.viewStats.storeFiles.length > 0) {
      recommendations.push('检查状态管理文件是否需要升级到 Vuex 4 或考虑迁移到 Pinia');
    }

    if (this.viewStats.viewTypes.dashboard > 0) {
      recommendations.push('测试仪表板页面的图表和数据展示功能');
    }

    if (this.viewStats.viewTypes.auth > 0) {
      recommendations.push('验证认证页面的登录和权限控制功能');
    }

    if (this.stats.failed > 0) {
      recommendations.push('手动检查迁移失败的视图文件');
    }

    recommendations.push('测试页面路由跳转是否正常');
    recommendations.push('检查页面的响应式布局');
    recommendations.push('验证表单提交和数据交互功能');

    return recommendations;
  }
}

module.exports = ViewMigrator;
