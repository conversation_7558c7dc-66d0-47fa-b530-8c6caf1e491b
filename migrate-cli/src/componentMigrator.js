const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const CodeMigrator = require('./codeMigrator');

/**
 * 组件迁移器
 * 专门处理 components 目录的迁移和转换
 */
class ComponentMigrator extends CodeMigrator {
  constructor(inputPath, outputPath, options = {}) {
    super(inputPath, {
      srcDir: 'src/components',
      outputPath: outputPath,
      outputSrcDir: 'src/components',
      includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
      excludePatterns: ['node_modules/**', 'dist/**', 'build/**', '**/*.test.js', '**/*.spec.js'],
      copyMode: true,
      ...options
    });

    this.componentStats = {
      totalComponents: 0,
      migratedComponents: 0,
      failedComponents: 0,
      skippedComponents: 0,
      componentTypes: {
        basic: 0,        // 基础组件
        form: 0,         // 表单组件
        table: 0,        // 表格组件
        chart: 0,        // 图表组件
        layout: 0,       // 布局组件
        business: 0      // 业务组件
      }
    };
  }

  /**
   * 执行组件迁移
   */
  async migrateComponents() {
    try {
      console.log(chalk.blue('🧩 开始迁移 Vue 组件...'));
      console.log(chalk.gray(`源路径: ${path.join(this.inputPath, this.options.srcDir)}`));
      console.log(chalk.gray(`目标路径: ${path.join(this.outputPath, this.options.outputSrcDir)}`));

      // 检查源组件目录是否存在
      const sourceComponentsPath = path.join(this.inputPath, this.options.srcDir);
      if (!await fs.pathExists(sourceComponentsPath)) {
        console.log(chalk.yellow('⚠️  源项目中没有找到 components 目录，跳过组件迁移'));
        return { success: true, message: 'No components directory found' };
      }

      // 确保目标目录存在
      await this.ensureOutputDirectory();

      // 执行迁移
      const result = await this.migrate();

      // 分析组件类型
      await this.analyzeComponentTypes();

      // 打印组件迁移统计
      this.printComponentStats();

      return {
        success: true,
        stats: this.stats,
        componentStats: this.componentStats
      };

    } catch (error) {
      console.error(chalk.red('❌ 组件迁移失败:'), error.message);
      throw error;
    }
  }

  /**
   * 分析组件类型
   */
  async analyzeComponentTypes() {
    const componentsPath = path.join(this.inputPath, this.options.srcDir);
    
    try {
      const files = await this.getFilesToMigrate();
      this.componentStats.totalComponents = files.filter(file => file.endsWith('.vue')).length;

      for (const filePath of files) {
        if (path.extname(filePath) === '.vue') {
          const componentType = await this.detectComponentType(filePath);
          this.componentStats.componentTypes[componentType]++;
        }
      }
    } catch (error) {
      console.warn(chalk.yellow('⚠️  组件类型分析失败:'), error.message);
    }
  }

  /**
   * 检测组件类型
   */
  async detectComponentType(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const fileName = path.basename(filePath, '.vue').toLowerCase();

      // 基于文件名和内容检测组件类型
      if (fileName.includes('form') || content.includes('el-form') || content.includes('<form')) {
        return 'form';
      } else if (fileName.includes('table') || content.includes('el-table') || content.includes('<table')) {
        return 'table';
      } else if (fileName.includes('chart') || content.includes('echarts') || content.includes('chart')) {
        return 'chart';
      } else if (fileName.includes('layout') || fileName.includes('header') || fileName.includes('sidebar') || fileName.includes('footer')) {
        return 'layout';
      } else if (fileName.includes('business') || filePath.includes('/business/')) {
        return 'business';
      } else {
        return 'basic';
      }
    } catch (error) {
      return 'basic';
    }
  }

  /**
   * 迁移 Vue 文件（重写以添加组件特定的转换）
   */
  async migrateVueFile(source, filePath) {
    try {
      // 先执行基础的 Vue 文件迁移
      let transformedCode = await super.migrateVueFile(source, filePath);

      // 应用组件特定的转换
      transformedCode = this.applyComponentSpecificTransforms(transformedCode, filePath);

      return transformedCode;
    } catch (error) {
      throw new Error(`组件文件转换失败: ${error.message}`);
    }
  }

  /**
   * 应用组件特定的转换
   */
  applyComponentSpecificTransforms(code, filePath) {
    const fileName = path.basename(filePath, '.vue').toLowerCase();

    // Element UI 组件特定转换
    code = this.transformElementUIComponents(code);

    // 表单组件特定转换
    if (fileName.includes('form')) {
      code = this.transformFormComponents(code);
    }

    // 表格组件特定转换
    if (fileName.includes('table')) {
      code = this.transformTableComponents(code);
    }

    // 布局组件特定转换
    if (fileName.includes('layout') || fileName.includes('header') || fileName.includes('sidebar')) {
      code = this.transformLayoutComponents(code);
    }

    return code;
  }

  /**
   * 转换 Element UI 组件
   */
  transformElementUIComponents(code) {
    // 常见的 Element UI 到 Element Plus 组件转换
    const componentMappings = {
      'el-date-picker': 'el-date-picker', // 大部分保持不变
      'el-time-picker': 'el-time-picker',
      'el-color-picker': 'el-color-picker',
      // 一些需要特殊处理的组件
    };

    // 处理一些属性变化
    code = code.replace(/placement="bottom-end"/g, 'placement="bottom-start"');
    
    // 处理图标变化
    code = code.replace(/el-icon-([a-zA-Z-]+)/g, (match, iconName) => {
      // Element Plus 使用新的图标系统
      return `<el-icon><${this.convertIconName(iconName)} /></el-icon>`;
    });

    return code;
  }

  /**
   * 转换图标名称
   */
  convertIconName(iconName) {
    const iconMappings = {
      'arrow-down': 'ArrowDown',
      'arrow-up': 'ArrowUp',
      'arrow-left': 'ArrowLeft',
      'arrow-right': 'ArrowRight',
      'plus': 'Plus',
      'minus': 'Minus',
      'delete': 'Delete',
      'edit': 'Edit',
      'search': 'Search',
      'close': 'Close',
      'check': 'Check',
      'warning': 'Warning',
      'info': 'InfoFilled',
      'success': 'SuccessFilled',
      'error': 'CircleCloseFilled'
    };

    return iconMappings[iconName] || iconName.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join('');
  }

  /**
   * 转换表单组件
   */
  transformFormComponents(code) {
    // 表单验证规则的转换
    code = code.replace(/rules="\{([^}]+)\}"/g, (match, rules) => {
      // 处理表单验证规则的变化
      return match; // 大部分规则保持不变
    });

    return code;
  }

  /**
   * 转换表格组件
   */
  transformTableComponents(code) {
    // 表格相关的特殊转换
    return code;
  }

  /**
   * 转换布局组件
   */
  transformLayoutComponents(code) {
    // 布局相关的特殊转换
    return code;
  }

  /**
   * 打印组件迁移统计
   */
  printComponentStats() {
    console.log('\n' + chalk.bold('🧩 组件迁移统计:'));
    console.log(`总组件数: ${this.componentStats.totalComponents}`);
    
    console.log('\n组件类型分布:');
    Object.entries(this.componentStats.componentTypes).forEach(([type, count]) => {
      if (count > 0) {
        const typeNames = {
          basic: '基础组件',
          form: '表单组件',
          table: '表格组件',
          chart: '图表组件',
          layout: '布局组件',
          business: '业务组件'
        };
        console.log(`  ${typeNames[type]}: ${count} 个`);
      }
    });

    // 调用父类的统计打印
    this.printMigrationStats();
  }

  /**
   * 生成组件迁移报告
   */
  generateComponentReport() {
    return {
      timestamp: new Date().toISOString(),
      inputPath: this.inputPath,
      outputPath: this.outputPath,
      stats: this.stats,
      componentStats: this.componentStats,
      recommendations: this.generateComponentRecommendations()
    };
  }

  /**
   * 生成组件迁移建议
   */
  generateComponentRecommendations() {
    const recommendations = [];

    if (this.componentStats.componentTypes.chart > 0) {
      recommendations.push('检查图表组件是否需要更新 ECharts 版本');
    }

    if (this.componentStats.componentTypes.form > 0) {
      recommendations.push('验证表单组件的验证规则是否正常工作');
    }

    if (this.componentStats.componentTypes.table > 0) {
      recommendations.push('测试表格组件的排序和筛选功能');
    }

    if (this.stats.failed > 0) {
      recommendations.push('手动检查迁移失败的组件');
    }

    recommendations.push('运行组件单元测试确保功能正常');
    recommendations.push('检查组件的样式是否需要调整');

    return recommendations;
  }
}

module.exports = ComponentMigrator;
