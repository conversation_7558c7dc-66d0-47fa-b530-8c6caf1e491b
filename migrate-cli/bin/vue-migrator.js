#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');

// 导入所有模块
const PackageUpgrader = require('../src/packageUpgrader');
const PackageComparator = require('../src/packageComparator');
const DependencyChecker = require('../src/dependencyChecker');
const CodeMigrator = require('../src/codeMigrator');
const ComponentMigrator = require('../src/componentMigrator');
const ViewMigrator = require('../src/viewMigrator');
const FailureLogger = require('../src/failureLogger');
const AIRepairer = require('../src/aiRepairer');
const ESLintFixer = require('../src/eslintFixer');
const BuildFixer = require('../src/buildFixer');

/**
 * 统一的 Vue 2 到 Vue 3 迁移工具
 * 整合了完整迁移和分步执行功能
 */
class UnifiedVueMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      skipDependencyCheck: options.skipDependencyCheck || false,
      skipAIRepair: options.skipAIRepair || false,
      skipESLint: options.skipESLint || false,
      skipBuild: options.skipBuild || false,
      aiApiKey: options.aiApiKey || process.env.OPENAI_API_KEY,
      buildCommand: options.buildCommand || 'npm run build',
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      ...options
    };

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      totalSteps: 7,
      completedSteps: 0,
      success: false,
      errors: [],
      stepResults: {}
    };

    this.failedFiles = [];
    this.spinner = null;
  }

  /**
   * 执行完整的 7 步迁移流程
   */
  async migrate() {
    try {
      this.printHeader();

      // 验证项目
      await this.validateProject();

      // 执行 7 个步骤
      await this.step1_upgradePackageJson();
      await this.step2_checkDependencies();
      await this.step3_migrateCode();
      await this.step4_logFailures();
      await this.step5_aiRepair();
      await this.step6_eslintFix();
      await this.step7_buildAndFix();

      // 完成迁移
      await this.completeMigration();

    } catch (error) {
      await this.handleMigrationError(error);
      throw error;
    }
  }

  /**
   * 执行单个步骤
   */
  async executeStep(stepNumber) {
    try {
      this.printHeader();
      await this.validateProject();

      switch (stepNumber) {
        case 1:
          await this.step1_upgradePackageJson();
          break;
        case 2:
          await this.step2_checkDependencies();
          break;
        case 3:
          await this.step3_migrateCode();
          break;
        case 4:
          await this.step4_logFailures();
          break;
        case 5:
          await this.step5_aiRepair();
          break;
        case 6:
          await this.step6_eslintFix();
          break;
        case 7:
          await this.step7_buildAndFix();
          break;
        default:
          throw new Error(`无效的步骤号: ${stepNumber}`);
      }

      console.log(chalk.green(`\n✅ 步骤 ${stepNumber} 执行完成！`));

    } catch (error) {
      console.error(chalk.red(`\n❌ 步骤 ${stepNumber} 执行失败:`), error.message);
      throw error;
    }
  }

  printHeader() {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 统一迁移工具\n'));
    console.log(chalk.gray(`项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}`));
    console.log(chalk.gray(`模式: ${this.options.dryRun ? '预览模式' : '实际执行'}\n`));
  }

  async validateProject() {
    this.spinner = ora('验证项目结构...').start();

    try {
      // 检查项目目录是否存在
      if (!await fs.pathExists(this.projectPath)) {
        throw new Error(`项目目录不存在: ${this.projectPath}`);
      }

      // 检查 package.json
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error('未找到 package.json 文件');
      }

      // 检查是否为 Vue 项目
      const packageJson = await fs.readJson(packageJsonPath);
      if (!packageJson.dependencies?.vue && !packageJson.devDependencies?.vue) {
        throw new Error('这不是一个 Vue 项目');
      }

      // 检查 Vue 版本
      const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
      if (vueVersion && vueVersion.startsWith('3.')) {
        console.log(chalk.yellow('⚠️  检测到 Vue 3 项目，可能不需要迁移'));
      }

      this.spinner.succeed('项目验证通过');
    } catch (error) {
      this.spinner.fail('项目验证失败');
      throw error;
    }
  }

  /**
   * 步骤 1: 升级 package.json 依赖
   */
  async step1_upgradePackageJson() {
    if (this.options.skipDependencyCheck && this.stats.completedSteps >= 1) {
      this.skipStep('package.json 依赖升级');
      return;
    }

    this.spinner = ora('步骤 1/7: 升级 package.json 依赖...').start();

    try {
      const upgrader = new PackageUpgrader(this.projectPath);
      const result = await upgrader.upgrade();

      this.stats.stepResults.packageUpgrade = result;
      this.completeStep();

      this.spinner.succeed('步骤 1/7: package.json 依赖升级完成');

      if (this.options.verbose && result.changes?.length > 0) {
        console.log(chalk.gray('  变更详情:'));
        result.changes.forEach(change => console.log(chalk.gray(`    ${change}`)));
      }
    } catch (error) {
      this.spinner.fail('步骤 1/7: package.json 依赖升级失败');
      this.stats.errors.push({ step: 1, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 2: 检查依赖兼容性
   */
  async step2_checkDependencies() {
    if (this.options.skipDependencyCheck) {
      this.skipStep('依赖兼容性检查');
      return;
    }

    this.spinner = ora('步骤 2/7: 检查依赖兼容性...').start();

    try {
      const checker = new DependencyChecker(this.projectPath);
      const result = await checker.checkCompatibility();

      this.stats.stepResults.dependencyCheck = result;
      this.completeStep();

      this.spinner.succeed('步骤 2/7: 依赖兼容性检查完成');

      // 如果有不兼容的依赖，给出警告
      if (result.incompatible?.length > 0) {
        console.log(chalk.yellow(`⚠️  发现 ${result.incompatible.length} 个不兼容的依赖`));
        if (this.options.verbose) {
          result.incompatible.forEach(dep => console.log(chalk.gray(`    ${dep}`)));
        }
      }
    } catch (error) {
      this.spinner.fail('步骤 2/7: 依赖兼容性检查失败');
      this.stats.errors.push({ step: 2, error: error.message });
      // 这个步骤失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  依赖检查失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 3: 批量迁移代码文件
   */
  async step3_migrateCode() {
    this.spinner = ora('步骤 3/7: 批量迁移代码文件...').start();

    try {
      const migrator = new CodeMigrator(this.projectPath);
      const result = await migrator.migrate();

      this.stats.stepResults.codeMigration = result;
      this.failedFiles = migrator.getFailedFiles();

      // 如果有失败文件，显示详细信息
      if (this.failedFiles.length > 0 && this.options.verbose) {
        console.log(chalk.yellow(`\n⚠️  发现 ${this.failedFiles.length} 个转换失败的文件:`));
        this.failedFiles.forEach(file => {
          console.log(chalk.gray(`  ${file.file} - ${file.errorType}: ${file.error.substring(0, 100)}...`));
        });
      }

      this.completeStep();

      this.spinner.succeed('步骤 3/7: 代码文件迁移完成');

      if (this.options.verbose) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 3/7: 代码文件迁移失败');
      this.stats.errors.push({ step: 3, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 4: 记录失败文件
   */
  async step4_logFailures() {
    this.spinner = ora('步骤 4/7: 记录失败文件...').start();

    try {
      const failureLogger = new FailureLogger(this.projectPath);
      await failureLogger.initialize();

      // 记录所有失败的文件
      if (this.failedFiles && this.failedFiles.length > 0) {
        for (const failedFile of this.failedFiles) {
          await failureLogger.logFailure(
            failedFile.file,
            new Error(failedFile.error),
            { step: 'code-migration' }
          );
        }
      }

      await failureLogger.saveFailures();
      this.stats.stepResults.failureLogging = {
        failedCount: this.failedFiles.length
      };
      this.completeStep();

      if (this.failedFiles.length > 0) {
        this.spinner.warn(`步骤 4/7: 记录了 ${this.failedFiles.length} 个失败文件`);
      } else {
        this.spinner.succeed('步骤 4/7: 没有失败文件需要记录');
      }
    } catch (error) {
      this.spinner.fail('步骤 4/7: 失败文件记录失败');
      this.stats.errors.push({ step: 4, error: error.message });
      // 这个步骤失败不应该中断整个流程
      this.completeStep();
    }
  }

  /**
   * 步骤 5: AI 修复失败文件
   */
  async step5_aiRepair() {
    if (this.options.skipAIRepair) {
      this.skipStep('AI 修复');
      return;
    }

    this.spinner = ora('步骤 5/7: AI 修复失败文件...').start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });

      if (!aiRepairer.isEnabled()) {
        this.spinner.warn('步骤 5/7: AI 修复不可用（缺少 API Key）');
        this.completeStep();
        return;
      }

      if (this.failedFiles.length === 0) {
        this.spinner.succeed('步骤 5/7: 没有需要 AI 修复的文件');
        this.completeStep();
        return;
      }

      console.log(chalk.blue(`\n🤖 准备使用 AI 修复 ${this.failedFiles.length} 个失败文件...`));

      // 按错误类型分组显示
      const errorGroups = {};
      this.failedFiles.forEach(file => {
        if (!errorGroups[file.errorType]) {
          errorGroups[file.errorType] = [];
        }
        errorGroups[file.errorType].push(file);
      });

      if (this.options.verbose) {
        Object.entries(errorGroups).forEach(([errorType, files]) => {
          console.log(chalk.gray(`  ${errorType}: ${files.length} 个文件`));
        });
      }

      const result = await aiRepairer.repairFailedFiles(this.failedFiles, this.projectPath);
      this.stats.stepResults.aiRepair = result;
      this.completeStep();

      this.spinner.succeed('步骤 5/7: AI 修复完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 5/7: AI 修复失败');
      this.stats.errors.push({ step: 5, error: error.message });
      // AI 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  AI 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 6: ESLint 自动修复
   */
  async step6_eslintFix() {
    if (this.options.skipESLint) {
      this.skipStep('ESLint 修复');
      return;
    }

    this.spinner = ora('步骤 6/7: ESLint 自动修复...').start();

    try {
      const eslintFixer = new ESLintFixer(this.projectPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        this.spinner.warn('步骤 6/7: ESLint 不可用，跳过此步骤');
        this.completeStep();
        return;
      }

      const result = await eslintFixer.fix();
      this.stats.stepResults.eslintFix = result;
      this.completeStep();

      this.spinner.succeed('步骤 6/7: ESLint 修复完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  修复文件: ${result.filesFixed || 0}, 修复错误: ${result.errorsFixed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 6/7: ESLint 修复失败');
      this.stats.errors.push({ step: 6, error: error.message });
      // ESLint 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  ESLint 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 7: 构建项目并修复错误
   */
  async step7_buildAndFix() {
    if (this.options.skipBuild) {
      this.skipStep('构建修复');
      return;
    }

    this.spinner = ora('步骤 7/7: 构建项目并修复错误...').start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });
      const buildFixer = new BuildFixer(this.projectPath, {
        buildCommand: this.options.buildCommand,
        aiRepairer: aiRepairer.isEnabled() ? aiRepairer : null
      });

      const result = await buildFixer.buildAndFix();
      this.stats.stepResults.buildFix = result;
      this.completeStep();

      if (result.success) {
        this.spinner.succeed('步骤 7/7: 项目构建成功');
      } else {
        this.spinner.warn('步骤 7/7: 项目构建仍有问题，需要手动修复');
      }

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  构建尝试: ${result.buildAttempts || 1}, 最终成功: ${result.success ? '是' : '否'}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 7/7: 构建修复失败');
      this.stats.errors.push({ step: 7, error: error.message });
      // 构建失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  构建修复失败，可能需要手动处理'));
      this.completeStep();
    }
  }

  /**
   * 完成迁移
   */
  async completeMigration() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = this.stats.errors.length === 0;

    console.log('\n' + chalk.bold.green('🎉 Vue 2 到 Vue 3 迁移完成!'));
    this.printFinalStats();

    // 生成迁移报告
    await this.generateMigrationReport();

    // 显示后续建议
    this.printRecommendations();
  }

  /**
   * 处理迁移错误
   */
  async handleMigrationError(error) {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = false;

    console.log('\n' + chalk.bold.red('❌ 迁移过程中发生错误'));
    console.log(chalk.red(error.message));

    if (this.options.verbose) {
      console.log(chalk.gray(error.stack));
    }

    await this.generateMigrationReport();
  }

  /**
   * 跳过步骤
   */
  skipStep(stepName) {
    console.log(chalk.gray(`⏭️  跳过: ${stepName}`));
    this.completeStep();
  }

  /**
   * 完成步骤
   */
  completeStep() {
    this.stats.completedSteps++;
  }

  /**
   * 打印最终统计
   */
  printFinalStats() {
    const duration = Math.round(this.stats.duration / 1000);

    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`完成步骤: ${this.stats.completedSteps}/${this.stats.totalSteps}`);
    console.log(`错误数量: ${this.stats.errors.length}`);

    if (this.stats.stepResults.codeMigration) {
      const cm = this.stats.stepResults.codeMigration;
      console.log(`代码文件: ${cm.success || 0} 成功, ${cm.failed || 0} 失败`);
    }

    if (this.stats.stepResults.buildFix?.success) {
      console.log(chalk.green('✅ 项目可以成功构建'));
    } else if (this.stats.stepResults.buildFix) {
      console.log(chalk.yellow('⚠️  项目构建可能仍有问题'));
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport() {
    const reportPath = path.join(this.projectPath, 'migration-report.json');

    const report = {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      options: this.options,
      stats: this.stats,
      success: this.stats.success,
      duration: this.stats.duration,
      recommendations: this.generateRecommendations()
    };

    if (!this.options.dryRun) {
      await fs.writeJson(reportPath, report, { spaces: 2 });
    }

    console.log(chalk.blue(`📄 迁移报告已生成: ${reportPath}`));
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.errors.length > 0) {
      recommendations.push('检查错误日志并手动修复剩余问题');
    }

    if (this.stats.stepResults.codeMigration?.failed > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    if (!this.stats.stepResults.buildFix?.success) {
      recommendations.push('运行构建命令检查剩余的构建错误');
    }

    recommendations.push('运行 npm install 安装新依赖');
    recommendations.push('运行测试确保功能正常');
    recommendations.push('检查 UI 组件是否正确迁移到 Element Plus');
    recommendations.push('更新文档和部署配置');

    return recommendations;
  }

  /**
   * 打印建议
   */
  printRecommendations() {
    const recommendations = this.generateRecommendations();

    if (recommendations.length > 0) {
      console.log('\n' + chalk.bold('💡 后续建议:'));
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

}

// CLI 接口
const program = new Command();

program
  .name('vue-migrator')
  .description('Vue 2 到 Vue 3 统一迁移工具')
  .version('1.0.0');

// 完整迁移命令
program
  .command('migrate')
  .description('执行完整的 Vue 2 到 Vue 3 迁移（7个步骤）')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('--skip-dependency-check', '跳过依赖兼容性检查')
  .option('--skip-ai', '跳过 AI 修复步骤')
  .option('--skip-eslint', '跳过 ESLint 自动修复')
  .option('--skip-build', '跳过构建和构建错误修复')
  .option('--ai-key <key>', 'OpenAI API Key')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, options) => {
    try {
      const migrator = new UnifiedVueMigrator(projectPath, {
        skipDependencyCheck: options.skipDependencyCheck,
        skipAIRepair: options.skipAi,
        skipESLint: options.skipEslint,
        skipBuild: options.skipBuild,
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await migrator.migrate();

    } catch (error) {
      console.error(chalk.red('\n❌ 迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 单步执行命令
program
  .command('step <number> [project-path]')
  .description('执行指定的迁移步骤 (1-7)')
  .option('--ai-key <key>', 'OpenAI API Key')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (stepNumber, projectPath = process.cwd(), options) => {
    try {
      const step = parseInt(stepNumber);
      if (step < 1 || step > 7) {
        console.error(chalk.red('❌ 步骤号必须在 1-7 之间'));
        process.exit(1);
      }

      const migrator = new UnifiedVueMigrator(projectPath, {
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await migrator.executeStep(step);

    } catch (error) {
      console.error(chalk.red('\n❌ 步骤执行失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 新旧工程迁移命令
program
  .command('migrate-to <old-project> <new-project>')
  .description('从旧 Vue 2 工程迁移到新 Vue 3 工程')
  .option('--compare-only', '仅对比 package.json，不执行迁移')
  .option('--components-only', '仅迁移组件')
  .option('--views-only', '仅迁移视图')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      console.log(chalk.bold.blue('\n🔄 Vue 2 到 Vue 3 工程迁移工具\n'));
      console.log(chalk.gray(`旧工程: ${oldProjectPath}`));
      console.log(chalk.gray(`新工程: ${newProjectPath}`));

      // 验证项目路径
      if (!await fs.pathExists(oldProjectPath)) {
        throw new Error(`旧项目路径不存在: ${oldProjectPath}`);
      }
      if (!await fs.pathExists(newProjectPath)) {
        throw new Error(`新项目路径不存在: ${newProjectPath}`);
      }

      // 1. 对比 package.json
      console.log(chalk.blue('\n📦 步骤 1: 对比 package.json'));
      const comparator = new PackageComparator(oldProjectPath, newProjectPath);
      const comparison = await comparator.compare();

      if (options.compareOnly) {
        console.log(chalk.green('\n✅ package.json 对比完成！'));
        return;
      }

      // 2. 迁移组件
      if (!options.viewsOnly) {
        console.log(chalk.blue('\n🧩 步骤 2: 迁移组件'));
        const componentMigrator = new ComponentMigrator(oldProjectPath, newProjectPath, {
          dryRun: options.dryRun,
          verbose: options.verbose
        });
        await componentMigrator.migrateComponents();
      }

      // 3. 迁移视图
      if (!options.componentsOnly) {
        console.log(chalk.blue('\n📄 步骤 3: 迁移视图'));
        const viewMigrator = new ViewMigrator(oldProjectPath, newProjectPath, {
          dryRun: options.dryRun,
          verbose: options.verbose
        });
        await viewMigrator.migrateViews();
      }

      console.log(chalk.bold.green('\n🎉 工程迁移完成！'));
      console.log(chalk.yellow('\n💡 后续建议:'));
      console.log('1. 检查迁移后的代码是否正常工作');
      console.log('2. 运行测试确保功能正常');
      console.log('3. 更新路由和状态管理配置');
      console.log('4. 检查样式是否需要调整');

    } catch (error) {
      console.error(chalk.red('\n❌ 工程迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 对比 package.json 命令
program
  .command('compare <old-project> <new-project>')
  .description('对比新旧工程的 package.json')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      const comparator = new PackageComparator(oldProjectPath, newProjectPath);
      const comparison = await comparator.compare();

      if (options.verbose) {
        console.log('\n详细对比结果:', JSON.stringify(comparison, null, 2));
      }

    } catch (error) {
      console.error(chalk.red('\n❌ 对比失败:'), error.message);
      process.exit(1);
    }
  });

// 迁移组件命令
program
  .command('migrate-components <old-project> <new-project>')
  .description('迁移组件从旧工程到新工程')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      const componentMigrator = new ComponentMigrator(oldProjectPath, newProjectPath, {
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await componentMigrator.migrateComponents();

    } catch (error) {
      console.error(chalk.red('\n❌ 组件迁移失败:'), error.message);
      process.exit(1);
    }
  });

// 迁移视图命令
program
  .command('migrate-views <old-project> <new-project>')
  .description('迁移视图从旧工程到新工程')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      const viewMigrator = new ViewMigrator(oldProjectPath, newProjectPath, {
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await viewMigrator.migrateViews();

    } catch (error) {
      console.error(chalk.red('\n❌ 视图迁移失败:'), error.message);
      process.exit(1);
    }
  });

// 显示步骤说明
program
  .command('steps')
  .description('显示迁移步骤说明')
  .action(() => {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移步骤说明\n'));

    console.log(chalk.bold('传统迁移模式（原地修改）:'));
    const steps = [
      '1. 升级 package.json 依赖 - 将 Vue 相关依赖升级到 Vue 3 版本',
      '2. 检查依赖兼容性 - 检查第三方依赖是否支持 Vue 3',
      '3. 批量迁移代码文件 - 使用 Gogocode 转换 .vue 和 .js 文件',
      '4. 记录失败文件 - 记录转换失败的文件供后续处理',
      '5. AI 修复失败文件 - 使用 AI 自动修复转换失败的文件',
      '6. ESLint 自动修复 - 运行 ESLint 修复格式和语法问题',
      '7. 构建项目并修复错误 - 尝试构建项目并使用 AI 修复构建错误'
    ];

    steps.forEach((step, index) => {
      console.log(chalk.green(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.bold('新工程迁移模式（推荐）:'));
    const newSteps = [
      '1. 对比 package.json - 分析新旧工程的依赖差异',
      '2. 迁移组件 - 将 components 目录转换并复制到新工程',
      '3. 迁移视图 - 将 views 目录转换并复制到新工程'
    ];

    newSteps.forEach((step, index) => {
      console.log(chalk.blue(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.yellow('💡 提示:'));
    console.log('传统模式:');
    console.log('- 使用 "vue-migrator migrate" 执行完整迁移');
    console.log('- 使用 "vue-migrator step <number>" 执行单个步骤');
    console.log('\n新工程模式（推荐）:');
    console.log('- 使用 "vue-migrator migrate-to <old> <new>" 执行完整迁移');
    console.log('- 使用 "vue-migrator compare <old> <new>" 仅对比 package.json');
    console.log('- 使用 "vue-migrator migrate-components <old> <new>" 仅迁移组件');
    console.log('- 使用 "vue-migrator migrate-views <old> <new>" 仅迁移视图');
    console.log('- 使用 --dry-run 选项预览变更而不实际修改文件');
  });

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  if (process.env.DEBUG) {
    console.error(promise);
  }
  process.exit(1);
});

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}

module.exports = UnifiedVueMigrator;
